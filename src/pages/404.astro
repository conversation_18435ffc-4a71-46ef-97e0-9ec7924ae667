---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
---

<Layout title="Page Not Found - Sassify">
  <Header />
  <main>
    <section class="py-20 md:py-32">
      <div class="container-custom text-center">
        <h1 class="text-9xl font-bold text-primary-600 dark:text-primary-400 mb-8">404</h1>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">Page Not Found</h2>
        <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
          The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
        </p>
        <div class="flex justify-center gap-4 flex-wrap">
          <a href="/" class="btn-primary">
            Return Home
          </a>
          <a href="/contact" class="btn-secondary">
            Contact Support
          </a>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</Layout> 

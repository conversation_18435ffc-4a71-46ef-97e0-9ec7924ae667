---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import HeroSection from '../components/HeroSection.astro';
---

<Layout title="Integrations - Sassify">
  <Header />
  <main>
    <HeroSection 
      title="Integrations & API" 
      highlightText="Integrations"
      description="Connect Sassify with your favorite tools and services to streamline your workflow."
    />
    
    <section class="py-16 bg-white dark:bg-gray-800">
      <div class="container-custom">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Connect With Your Favorite Tools</h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Sassify integrates with over 50 popular tools and services to help you create seamless workflows and maximize productivity.
          </p>
        </div>
        
        <!-- Integration Categories -->
        <div class="flex flex-wrap justify-center gap-4 mb-12">
          <a href="#all" class="px-4 py-2 rounded-full bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 font-medium text-sm hover:bg-primary-200 dark:hover:bg-primary-800 transition-colors">
            All Integrations
          </a>
          <a href="#crm" class="px-4 py-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            CRM & Sales
          </a>
          <a href="#marketing" class="px-4 py-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Marketing
          </a>
          <a href="#productivity" class="px-4 py-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Productivity
          </a>
          <a href="#communication" class="px-4 py-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Communication
          </a>
          <a href="#developer" class="px-4 py-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Developer Tools
          </a>
        </div>
        
        <!-- Integration Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <!-- Integration Card -->
          <div class="card p-6 border border-gray-200 dark:border-gray-700 flex flex-col items-center text-center">
            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/slack/slack-original.svg" alt="Slack" class="w-16 h-16 mb-4" />
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Slack</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              Get notifications and updates directly in your Slack channels. Share reports and collaborate with your team.
            </p>
            <div class="mt-auto">
              <a href="#" class="text-primary-600 dark:text-primary-400 font-medium hover:underline">Learn More</a>
            </div>
          </div>
          
          <!-- Integration Card -->
          <div class="card p-6 border border-gray-200 dark:border-gray-700 flex flex-col items-center text-center">
            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/google/google-original.svg" alt="Google Workspace" class="w-16 h-16 mb-4" />
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Google Workspace</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              Sync with Google Calendar, import data from Google Sheets, and export reports to Google Drive.
            </p>
            <div class="mt-auto">
              <a href="#" class="text-primary-600 dark:text-primary-400 font-medium hover:underline">Learn More</a>
            </div>
          </div>
          
          <!-- Integration Card -->
          <div class="card p-6 border border-gray-200 dark:border-gray-700 flex flex-col items-center text-center">
            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/salesforce/salesforce-original.svg" alt="Salesforce" class="w-16 h-16 mb-4" />
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Salesforce</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              Integrate with Salesforce to sync customer data, track opportunities, and streamline your sales process.
            </p>
            <div class="mt-auto">
              <a href="#" class="text-primary-600 dark:text-primary-400 font-medium hover:underline">Learn More</a>
            </div>
          </div>
          
          <!-- Integration Card -->
          <div class="card p-6 border border-gray-200 dark:border-gray-700 flex flex-col items-center text-center">
            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/github/github-original.svg" alt="GitHub" class="w-16 h-16 mb-4" />
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">GitHub</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              Connect your GitHub repositories to track issues, pull requests, and code changes within Sassify.
            </p>
            <div class="mt-auto">
              <a href="#" class="text-primary-600 dark:text-primary-400 font-medium hover:underline">Learn More</a>
            </div>
          </div>
          
          <!-- Integration Card -->
          <div class="card p-6 border border-gray-200 dark:border-gray-700 flex flex-col items-center text-center">
            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/figma/figma-original.svg" alt="Figma" class="w-16 h-16 mb-4" />
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Figma</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              Connect Sassify with Figma to sync designs and collaborate with your team.
            </p>
            <div class="mt-auto">
              <a href="#" class="text-primary-600 dark:text-primary-400 font-medium hover:underline">Learn More</a>
            </div>
          </div>
          
          <!-- Integration Card -->
          <div class="card p-6 border border-gray-200 dark:border-gray-700 flex flex-col items-center text-center">
            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/jira/jira-original.svg" alt="Jira" class="w-16 h-16 mb-4" />
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Jira</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              Sync your Jira issues and projects with Sassify to keep your development and business teams aligned.
            </p>
            <div class="mt-auto">
              <a href="#" class="text-primary-600 dark:text-primary-400 font-medium hover:underline">Learn More</a>
            </div>
          </div>
        </div>
        
        <div class="text-center mb-16">
          <a href="#" class="btn-outline">
            View All Integrations
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </a>
        </div>
        
        <!-- API Section -->
        <div class="border-t border-gray-200 dark:border-gray-700 pt-16">
          <div class="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <span class="inline-block px-4 py-1 rounded-full bg-secondary-100 dark:bg-secondary-900 text-secondary-600 dark:text-secondary-300 font-medium text-sm mb-4">
                Developer API
              </span>
              <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">Build Custom Integrations</h2>
              <p class="text-gray-600 dark:text-gray-300 mb-6">
                Our comprehensive API allows you to build custom integrations and extend Sassify's functionality to meet your specific needs. With detailed documentation and developer support, you can create seamless connections between Sassify and your internal tools.
              </p>
              <div class="space-y-4 mb-8">
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mt-1 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <p class="text-gray-600 dark:text-gray-300">RESTful API with comprehensive endpoints</p>
                </div>
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mt-1 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <p class="text-gray-600 dark:text-gray-300">Secure authentication with OAuth 2.0</p>
                </div>
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mt-1 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <p class="text-gray-600 dark:text-gray-300">Webhooks for real-time event notifications</p>
                </div>
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mt-1 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <p class="text-gray-600 dark:text-gray-300">SDKs for popular programming languages</p>
                </div>
              </div>
              <div class="flex flex-col sm:flex-row gap-4">
                <a href="#" class="btn-primary">API Documentation</a>
                <a href="#" class="btn-secondary">Developer Portal</a>
              </div>
            </div>
            <div class="rounded-lg overflow-hidden shadow-xl">
              <div class="bg-gray-900 text-white p-4 font-mono text-sm">
                <div class="flex items-center mb-4">
                  <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                  <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                  <div class="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <pre class="text-green-400">
# Example API Request
curl -X GET \
  https://api.sassify.com/v1/users \
  -H 'Authorization: Bearer YOUR_API_KEY' \
  -H 'Content-Type: application/json'
                </pre>
                <pre class="text-blue-400 mt-4">
# Response
{`
  "users": [
    {
      "id": "usr_123456",
      "name": "John Doe",
      "email": "<EMAIL>",
      "created_at": "2023-06-15T10:30:00Z",
      "status": "active"
    },
    {
      "id": "usr_789012",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "created_at": "2023-06-10T14:20:00Z",
      "status": "active"
    }
  ],
  "meta": {
    "total": 2,
    "page": 1,
    "per_page": 10
  }
`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</Layout> 

---
import '../styles/global.css';
import '../styles/transitions.css';
import { ClientRouter } from 'astro:transitions';

export interface Props {
  title: string;
  description?: string;
}

const { 
  title, 
  description = "Modern SaaS landing page template with responsive design and modular sections."
} = Astro.props;
---

<!doctype html>
<html lang="en" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' }" x-init="$watch('darkMode', val => localStorage.setItem('darkMode', val))" :class="{ 'dark': darkMode }">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <meta name="description" content={description} />
    <title>{title}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lexend:wght@400;500;600;700&display=swap" rel="stylesheet">
    <ClientRouter />
  </head>
  <body class="antialiased bg-white dark:bg-secondary-950 text-secondary-900 dark:text-secondary-100 transition-colors duration-300">
    <div id="page-wrapper" class="page-transition-wrapper">
      <slot />
    </div>
    <script>
      // Check for user preference
      if (localStorage.getItem('darkMode') === null) {
        // Check system preference
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          localStorage.setItem('darkMode', 'true');
          document.documentElement.classList.add('dark');
        } else {
          localStorage.setItem('darkMode', 'false');
        }
      }

      // Handle page transitions
      document.addEventListener('astro:page-load', () => {
        // This runs on initial page load and after each navigation
        const pageWrapper = document.getElementById('page-wrapper');
        if (pageWrapper) {
          pageWrapper.classList.add('page-loaded');
        }
      });

      document.addEventListener('astro:before-swap', () => {
        // This runs before the new page content is swapped in
        const pageWrapper = document.getElementById('page-wrapper');
        if (pageWrapper) {
          pageWrapper.classList.remove('page-loaded');
        }
      });
    </script>
    <script src="../scripts/transitions.js"></script>
  </body>
</html>

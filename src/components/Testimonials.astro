---
const testimonials = [
  {
    content: "This platform has completely transformed how we manage our projects. The intuitive interface and powerful features have increased our team's productivity by over 40%.",
    author: "<PERSON>",
    position: "Product Manager",
    company: "TechCorp",
    avatar: "https://randomuser.me/api/portraits/women/32.jpg"
  },
  {
    content: "I've tried many SaaS solutions, but this one stands out for its ease of use and comprehensive feature set. The customer support is also exceptional.",
    author: "<PERSON>",
    position: "CTO",
    company: "Innovate Inc.",
    avatar: "https://randomuser.me/api/portraits/men/46.jpg"
  },
  {
    content: "The analytics capabilities alone are worth the investment. We've gained valuable insights that have helped us optimize our operations and reduce costs.",
    author: "<PERSON>",
    position: "Operations Director",
    company: "Global Solutions",
    avatar: "https://randomuser.me/api/portraits/women/65.jpg"
  }
];
---

<section id="testimonials" class="section">
  <div class="container-custom">
    <div class="text-center max-w-3xl mx-auto mb-16">
      <span class="inline-block px-4 py-1 rounded-full bg-secondary-100 dark:bg-secondary-900 text-secondary-600 dark:text-secondary-300 font-medium text-sm mb-4">
        Customer Stories
      </span>
      <h2 class="mb-6 text-gray-900 dark:text-white">What Our Clients Say</h2>
      <p class="text-gray-600 dark:text-gray-300">
        Don't just take our word for it. Here's what our customers have to say about their experience with our platform.
      </p>
    </div>
    
    <div class="grid md:grid-cols-3 gap-8">
      {testimonials.map((testimonial, index) => (
        <div 
          class="card p-6 border border-gray-200 dark:border-gray-700 slide-up" 
          style={`animation-delay: ${index * 150}ms`}
        >
          <div class="mb-6">
            {[...Array(5)].map(() => (
              <svg class="w-5 h-5 inline-block text-warning-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
            ))}
          </div>
          
          <blockquote class="mb-6 text-gray-700 dark:text-gray-300 italic">"{testimonial.content}"</blockquote>
          
          <div class="flex items-center">
            <img 
              src={testimonial.avatar} 
              alt={testimonial.author} 
              class="w-12 h-12 rounded-full mr-4 object-cover"
              loading="lazy"
            />
            <div>
              <p class="font-semibold text-gray-900 dark:text-white">{testimonial.author}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">{testimonial.position}, {testimonial.company}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
    
    <div class="mt-16 bg-linear-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 md:p-12 relative overflow-hidden">
      <div class="absolute inset-0 bg-linear-to-r from-primary-600/30 to-secondary-600/30 backdrop-blur-xs"></div>
      <div class="relative z-10 text-center md:text-left md:flex items-center justify-between">
        <div class="mb-6 md:mb-0 md:mr-8">
          <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">Ready to transform your business?</h3>
          <p class="text-white/80 max-w-2xl">Join thousands of satisfied customers who have improved their workflow with our platform.</p>
        </div>
        <a href="#signup" class="inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-300 focus:outline-hidden focus:ring-2 focus:ring-offset-2 bg-white text-primary-600 hover:bg-gray-100 focus:ring-white">
          Get Started Today
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>